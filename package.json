{"name": "novel-reader-extension", "version": "1.0.0", "description": "优化小说阅读体验的浏览器插件", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "mkdir -p public/src/content && cp src/content/content.css public/src/content/ && vite build && node scripts/post-build.js", "preview": "vite preview", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "clean": "rm -rf dist", "zip": "cd dist && zip -r novel-reader-extension.zip .", "release": "npm run clean && npm run build && npm run zip"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "zustand": "^4.4.1"}, "devDependencies": {"@crxjs/vite-plugin": "^2.0.0-beta.19", "@types/chrome": "^0.0.245", "@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.5.0", "@typescript-eslint/parser": "^6.5.0", "@vitejs/plugin-react": "^4.0.4", "eslint": "^8.48.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "typescript": "^5.2.2", "vite": "^4.4.9"}, "keywords": ["browser-extension", "novel-reader", "reading-mode", "chrome-extension", "react", "typescript", "vite"], "author": {"name": "Novel Reader Team", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/novel-reader/extension.git"}, "bugs": {"url": "https://github.com/novel-reader/extension/issues"}, "homepage": "https://github.com/novel-reader/extension#readme"}