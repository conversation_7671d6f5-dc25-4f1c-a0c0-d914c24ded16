// src/content/FloatingButton.tsx
import React, { useState, useEffect } from "react";
import { useReaderStore } from "@/stores/readerStore";
import styles from "./floating.module.css";

interface FloatingButtonProps {
  onClick: () => void;
}

const FloatingButton: React.FC<FloatingButtonProps> = ({ onClick }) => {
  const { isReaderMode } = useReaderStore();
  const [show, setShow] = useState(false);
  const [animate, setAnimate] = useState(false);

  // 根据阅读模式状态控制显示/隐藏
  const isVisible = !isReaderMode;

  useEffect(() => {
    if (isVisible) {
      // 延迟显示，让页面加载完成
      const timer = setTimeout(() => {
        setShow(true);
        setAnimate(true);
      }, 1000);
      return () => clearTimeout(timer);
    } else {
      setShow(false);
      setAnimate(false);
    }
  }, [isVisible]);

  if (!show) return null;

  return (
    <div
      className={`${styles.floatingContainer} ${animate ? styles.animate : ""}`}
    >
      <button
        className={styles.floatingButton}
        onClick={onClick}
        title="进入阅读模式"
      >
        <div className={styles.buttonContent}>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V5H19V19M17,17H7V16H17V17M17,15H7V14H17V15M17,13H7V12H17V13M17,11H7V10H17V11M17,9H7V8H17V9Z" />
          </svg>
          <span className={styles.buttonText}>阅读模式</span>
        </div>

        {/* 脉冲动画圆圈 */}
        <div className={styles.pulseRing}></div>
        <div
          className={styles.pulseRing}
          style={{ animationDelay: "1s" }}
        ></div>
      </button>

      {/* 提示文字 */}
      <div className={styles.tooltip}>
        点击进入沉浸式阅读模式
        <div className={styles.tooltipArrow}></div>
      </div>
    </div>
  );
};

export default FloatingButton;
