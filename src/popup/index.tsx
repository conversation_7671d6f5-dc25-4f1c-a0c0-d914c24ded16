// src/popup/index.tsx
import React, { useState, useEffect } from "react";
import { createRoot } from "react-dom/client";
import styles from "./popup.module.css";

interface ReadingState {
  isReaderMode: boolean;
  currentChapter: string | null;
}

const Popup: React.FC = () => {
  const [readingState, setReadingState] = useState<ReadingState>({
    isReaderMode: false,
    currentChapter: null,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 获取当前标签页的阅读状态
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]?.id) {
        chrome.tabs.sendMessage(
          tabs[0].id,
          { type: "GET_READING_STATE" },
          (response) => {
            if (response) {
              setReadingState(response);
            }
            setLoading(false);
          }
        );
      } else {
        setLoading(false);
      }
    });
  }, []);

  const toggleReaderMode = () => {
    setLoading(true);
    console.log("Popup: Toggling reader mode...");

    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]?.id) {
        console.log("Popup: Sending message to tab:", tabs[0].id);
        chrome.tabs.sendMessage(
          tabs[0].id,
          { type: "TOGGLE_READER_MODE" },
          (response) => {
            console.log("Popup: Received response:", response);

            if (chrome.runtime.lastError) {
              console.error(
                "Popup: Chrome runtime error:",
                chrome.runtime.lastError
              );
              setLoading(false);
              return;
            }

            if (response && response.success) {
              setReadingState({
                isReaderMode: response.isReaderMode,
                currentChapter: response.currentChapter || null,
              });
              console.log("Popup: State updated:", response.isReaderMode);
            } else {
              console.error(
                "Popup: Failed to toggle reader mode:",
                response?.error
              );
            }
            setLoading(false);
          }
        );
      } else {
        console.error("Popup: No active tab found");
        setLoading(false);
      }
    });
  };

  const openOptions = () => {
    chrome.runtime.openOptionsPage();
  };

  if (loading) {
    return (
      <div className={styles.popup}>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>检测页面状态中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.popup}>
      <div className={styles.header}>
        <div className={styles.logo}>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V5H19V19M17,17H7V16H17V17M17,15H7V14H17V15M17,13H7V12H17V13M17,11H7V10H17V11M17,9H7V8H17V9Z" />
          </svg>
        </div>
        <div className={styles.title}>
          <h2>小说阅读助手</h2>
          <p>优化您的阅读体验</p>
        </div>
      </div>

      <div className={styles.content}>
        {readingState.isReaderMode ? (
          <div className={styles.readerActive}>
            <div className={styles.statusIcon}>
              <svg
                width="32"
                height="32"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M11,16.5L18,9.5L16.59,8.09L11,13.67L7.91,10.59L6.5,12L11,16.5Z" />
              </svg>
            </div>
            <h3>阅读模式已激活</h3>
            {readingState.currentChapter && (
              <p className={styles.chapterInfo}>
                正在阅读: {readingState.currentChapter}
              </p>
            )}
            <button
              className={`${styles.button} ${styles.secondary}`}
              onClick={toggleReaderMode}
            >
              退出阅读模式
            </button>
          </div>
        ) : (
          <div className={styles.readerInactive}>
            <div className={styles.statusIcon}>
              <svg
                width="32"
                height="32"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
              </svg>
            </div>
            <h3>在小说页面启用阅读模式</h3>
            <p>支持晋江、起点、纵横等主流网站</p>

            <div className={styles.features}>
              <div className={styles.feature}>
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M12,17.27L18.18,21L16.54,13.97L22,9.24L14.81,8.62L12,2L9.19,8.62L2,9.24L7.46,13.97L5.82,21L12,17.27Z" />
                </svg>
                <span>沉浸式阅读体验</span>
              </div>
              <div className={styles.feature}>
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
                </svg>
                <span>智能章节切换</span>
              </div>
              <div className={styles.feature}>
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z" />
                </svg>
                <span>个性化设置</span>
              </div>
              <div className={styles.feature}>
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M17,18L12,15.82L7,18V5H17M17,3H7A2,2 0 0,0 5,5V21L12,18L19,21V5C19,3.89 18.1,3 17,3Z" />
                </svg>
                <span>书签和历史记录</span>
              </div>
            </div>

            <button
              className={`${styles.button} ${styles.primary}`}
              onClick={toggleReaderMode}
            >
              启用阅读模式
            </button>
          </div>
        )}
      </div>

      <div className={styles.footer}>
        <button className={styles.footerButton} onClick={openOptions}>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z" />
          </svg>
          设置
        </button>

        <button
          className={styles.footerButton}
          onClick={() =>
            window.open("https://github.com/novel-reader-extension", "_blank")
          }
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,2.04C6.5,2.04 2,6.53 2,12.04C2,17.54 6.5,22.04 12,22.04C17.5,22.04 22,17.54 22,12.04C22,6.53 17.5,2.04 12,2.04M12,20.04A8,8 0 0,1 4,12.04A8,8 0 0,1 12,4.04A8,8 0 0,1 20,12.04A8,8 0 0,1 12,20.04M13,14.5V16.5H11V14.5H13M13,8.5V12.5H11V8.5H13Z" />
          </svg>
          帮助
        </button>
      </div>
    </div>
  );
};

// 渲染到页面
const container = document.getElementById("root");
if (container) {
  const root = createRoot(container);
  root.render(<Popup />);
}
