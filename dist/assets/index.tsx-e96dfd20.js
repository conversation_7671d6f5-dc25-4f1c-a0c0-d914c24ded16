import{r as j,g as J,R as K,j as e,c as P}from"./client-25e9187c.js";const F=t=>{let o;const n=new Set,a=(h,_)=>{const x=typeof h=="function"?h(o):h;if(!Object.is(x,o)){const f=o;o=_??(typeof x!="object"||x===null)?x:Object.assign({},o,x),n.forEach(y=>y(o,f))}},i=()=>o,v={setState:a,getState:i,getInitialState:()=>g,subscribe:h=>(n.add(h),()=>n.delete(h)),destroy:()=>{n.clear()}},g=o=t(a,i,v);return v},Y=t=>t?F(t):F;var V={exports:{}},$={},z={exports:{}},U={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var k=j;function X(t,o){return t===o&&(t!==0||1/t===1/o)||t!==t&&o!==o}var Q=typeof Object.is=="function"?Object.is:X,ee=k.useState,te=k.useEffect,ne=k.useLayoutEffect,oe=k.useDebugValue;function se(t,o){var n=o(),a=ee({inst:{value:n,getSnapshot:o}}),i=a[0].inst,s=a[1];return ne(function(){i.value=n,i.getSnapshot=o,I(i)&&s({inst:i})},[t,n,o]),te(function(){return I(i)&&s({inst:i}),t(function(){I(i)&&s({inst:i})})},[t]),oe(n),n}function I(t){var o=t.getSnapshot;t=t.value;try{var n=o();return!Q(t,n)}catch{return!0}}function re(t,o){return o()}var ae=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?re:se;U.useSyncExternalStore=k.useSyncExternalStore!==void 0?k.useSyncExternalStore:ae;z.exports=U;var ie=z.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var T=j,le=ie;function ce(t,o){return t===o&&(t!==0||1/t===1/o)||t!==t&&o!==o}var de=typeof Object.is=="function"?Object.is:ce,ue=le.useSyncExternalStore,he=T.useRef,me=T.useEffect,pe=T.useMemo,fe=T.useDebugValue;$.useSyncExternalStoreWithSelector=function(t,o,n,a,i){var s=he(null);if(s.current===null){var l={hasValue:!1,value:null};s.current=l}else l=s.current;s=pe(function(){function v(f){if(!g){if(g=!0,h=f,f=a(f),i!==void 0&&l.hasValue){var y=l.value;if(i(y,f))return _=y}return _=f}if(y=_,de(h,f))return y;var c=a(f);return i!==void 0&&i(y,c)?(h=f,y):(h=f,_=c)}var g=!1,h,_,x=n===void 0?null:n;return[function(){return v(o())},x===null?void 0:function(){return v(x())}]},[o,n,a,i]);var u=ue(t,s[0],s[1]);return me(function(){l.hasValue=!0,l.value=u},[u]),fe(u),u};V.exports=$;var ge=V.exports;const ve=J(ge),{useDebugValue:xe}=K,{useSyncExternalStoreWithSelector:ye}=ve;const _e=t=>t;function Se(t,o=_e,n){const a=ye(t.subscribe,t.getState,t.getServerState||t.getInitialState,o,n);return xe(a),a}const O=t=>{const o=typeof t=="function"?Y(t):t,n=(a,i)=>Se(o,a,i);return Object.assign(n,o),n},q=t=>t?O(t):O,H=new Map,R=t=>{const o=H.get(t);return o?Object.fromEntries(Object.entries(o.stores).map(([n,a])=>[n,a.getState()])):{}},be=(t,o,n)=>{if(t===void 0)return{type:"untracked",connection:o.connect(n)};const a=H.get(n.name);if(a)return{type:"tracked",store:t,...a};const i={connection:o.connect(n),stores:{}};return H.set(n.name,i),{type:"tracked",store:t,...i}},Ce=(t,o={})=>(n,a,i)=>{const{enabled:s,anonymousActionType:l,store:u,...v}=o;let g;try{g=(s??!1)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!g)return t(n,a,i);const{connection:h,..._}=be(u,g,v);let x=!0;i.setState=(c,m,d)=>{const p=n(c,m);if(!x)return p;const S=d===void 0?{type:l||"anonymous"}:typeof d=="string"?{type:d}:d;return u===void 0?(h==null||h.send(S,a()),p):(h==null||h.send({...S,type:`${u}/${S.type}`},{...R(v.name),[u]:i.getState()}),p)};const f=(...c)=>{const m=x;x=!1,n(...c),x=m},y=t(i.setState,a,i);if(_.type==="untracked"?h==null||h.init(y):(_.stores[_.store]=i,h==null||h.init(Object.fromEntries(Object.entries(_.stores).map(([c,m])=>[c,c===_.store?y:m.getState()])))),i.dispatchFromDevtools&&typeof i.dispatch=="function"){let c=!1;const m=i.dispatch;i.dispatch=(...d)=>{m(...d)}}return h.subscribe(c=>{var m;switch(c.type){case"ACTION":if(typeof c.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return M(c.payload,d=>{if(d.type==="__setState"){if(u===void 0){f(d.state);return}Object.keys(d.state).length!==1&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format. 
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);const p=d.state[u];if(p==null)return;JSON.stringify(i.getState())!==JSON.stringify(p)&&f(p);return}i.dispatchFromDevtools&&typeof i.dispatch=="function"&&i.dispatch(d)});case"DISPATCH":switch(c.payload.type){case"RESET":return f(y),u===void 0?h==null?void 0:h.init(i.getState()):h==null?void 0:h.init(R(v.name));case"COMMIT":if(u===void 0){h==null||h.init(i.getState());return}return h==null?void 0:h.init(R(v.name));case"ROLLBACK":return M(c.state,d=>{if(u===void 0){f(d),h==null||h.init(i.getState());return}f(d[u]),h==null||h.init(R(v.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return M(c.state,d=>{if(u===void 0){f(d);return}JSON.stringify(i.getState())!==JSON.stringify(d[u])&&f(d[u])});case"IMPORT_STATE":{const{nextLiftedState:d}=c.payload,p=(m=d.computedStates.slice(-1)[0])==null?void 0:m.state;if(!p)return;f(u===void 0?p:p[u]),h==null||h.send(null,d);return}case"PAUSE_RECORDING":return x=!x}return}}),y},je=Ce,M=(t,o)=>{let n;try{n=JSON.parse(t)}catch(a){console.error("[zustand devtools middleware] Could not parse the received json",a)}n!==void 0&&o(n)};function we(t,o){let n;try{n=t()}catch{return}return{getItem:i=>{var s;const l=v=>v===null?null:JSON.parse(v,o==null?void 0:o.reviver),u=(s=n.getItem(i))!=null?s:null;return u instanceof Promise?u.then(l):l(u)},setItem:(i,s)=>n.setItem(i,JSON.stringify(s,o==null?void 0:o.replacer)),removeItem:i=>n.removeItem(i)}}const E=t=>o=>{try{const n=t(o);return n instanceof Promise?n:{then(a){return E(a)(n)},catch(a){return this}}}catch(n){return{then(a){return this},catch(a){return E(a)(n)}}}},ke=(t,o)=>(n,a,i)=>{let s={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:m=>m,version:0,merge:(m,d)=>({...d,...m}),...o},l=!1;const u=new Set,v=new Set;let g;try{g=s.getStorage()}catch{}if(!g)return t((...m)=>{console.warn(`[zustand persist middleware] Unable to update item '${s.name}', the given storage is currently unavailable.`),n(...m)},a,i);const h=E(s.serialize),_=()=>{const m=s.partialize({...a()});let d;const p=h({state:m,version:s.version}).then(S=>g.setItem(s.name,S)).catch(S=>{d=S});if(d)throw d;return p},x=i.setState;i.setState=(m,d)=>{x(m,d),_()};const f=t((...m)=>{n(...m),_()},a,i);let y;const c=()=>{var m;if(!g)return;l=!1,u.forEach(p=>p(a()));const d=((m=s.onRehydrateStorage)==null?void 0:m.call(s,a()))||void 0;return E(g.getItem.bind(g))(s.name).then(p=>{if(p)return s.deserialize(p)}).then(p=>{if(p)if(typeof p.version=="number"&&p.version!==s.version){if(s.migrate)return s.migrate(p.state,p.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return p.state}).then(p=>{var S;return y=s.merge(p,(S=a())!=null?S:f),n(y,!0),_()}).then(()=>{d==null||d(y,void 0),l=!0,v.forEach(p=>p(y))}).catch(p=>{d==null||d(void 0,p)})};return i.persist={setOptions:m=>{s={...s,...m},m.getStorage&&(g=m.getStorage())},clearStorage:()=>{g==null||g.removeItem(s.name)},getOptions:()=>s,rehydrate:()=>c(),hasHydrated:()=>l,onHydrate:m=>(u.add(m),()=>{u.delete(m)}),onFinishHydration:m=>(v.add(m),()=>{v.delete(m)})},c(),y||f},Ne=(t,o)=>(n,a,i)=>{let s={storage:we(()=>localStorage),partialize:c=>c,version:0,merge:(c,m)=>({...m,...c}),...o},l=!1;const u=new Set,v=new Set;let g=s.storage;if(!g)return t((...c)=>{console.warn(`[zustand persist middleware] Unable to update item '${s.name}', the given storage is currently unavailable.`),n(...c)},a,i);const h=()=>{const c=s.partialize({...a()});return g.setItem(s.name,{state:c,version:s.version})},_=i.setState;i.setState=(c,m)=>{_(c,m),h()};const x=t((...c)=>{n(...c),h()},a,i);i.getInitialState=()=>x;let f;const y=()=>{var c,m;if(!g)return;l=!1,u.forEach(p=>{var S;return p((S=a())!=null?S:x)});const d=((m=s.onRehydrateStorage)==null?void 0:m.call(s,(c=a())!=null?c:x))||void 0;return E(g.getItem.bind(g))(s.name).then(p=>{if(p)if(typeof p.version=="number"&&p.version!==s.version){if(s.migrate)return[!0,s.migrate(p.state,p.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,p.state];return[!1,void 0]}).then(p=>{var S;const[N,B]=p;if(f=s.merge(B,(S=a())!=null?S:x),n(f,!0),N)return h()}).then(()=>{d==null||d(f,void 0),f=a(),l=!0,v.forEach(p=>p(f))}).catch(p=>{d==null||d(void 0,p)})};return i.persist={setOptions:c=>{s={...s,...c},c.storage&&(g=c.storage)},clearStorage:()=>{g==null||g.removeItem(s.name)},getOptions:()=>s,rehydrate:()=>y(),hasHydrated:()=>l,onHydrate:c=>(u.add(c),()=>{u.delete(c)}),onFinishHydration:c=>(v.add(c),()=>{v.delete(c)})},s.skipHydration||y(),f||x},Be=(t,o)=>"getStorage"in o||"serialize"in o||"deserialize"in o?ke(t,o):Ne(t,o),G=Be,w=q()(je(G((t,o)=>({isReaderMode:!1,currentChapter:null,readingPosition:0,showSettings:!1,showBookmarks:!1,settings:{fontSize:16,fontFamily:"Microsoft YaHei, -apple-system, BlinkMacSystemFont, sans-serif",lineHeight:1.8,theme:"light",pageWidth:800,autoScroll:!1,scrollSpeed:50},enterReaderMode:n=>{t({isReaderMode:!0,currentChapter:n,showSettings:!1,showBookmarks:!1})},exitReaderMode:()=>{t({isReaderMode:!1,showSettings:!1,showBookmarks:!1})},updateSettings:n=>{t(a=>({settings:{...a.settings,...n}}))},setCurrentChapter:n=>{t({currentChapter:n})},updateReadingPosition:n=>{t({readingPosition:n})},toggleSettings:()=>{t(n=>({showSettings:!n.showSettings,showBookmarks:!1}))},toggleBookmarks:()=>{t(n=>({showBookmarks:!n.showBookmarks,showSettings:!1}))},loadChapter:n=>{t({currentChapter:n})}}),{name:"novel-reader-settings",partialize:t=>({settings:t.settings})}))),A=q()(G((t,o)=>({history:[],currentHistoryIndex:-1,bookmarks:[],favorites:[],currentSession:[],currentSessionIndex:-1,loadingNext:!1,loadingPrev:!1,addToHistory:n=>{t(a=>{const i=a.history.findIndex(l=>l.id===n.id||l.url===n.url);let s=[...a.history];return i>=0?s[i]={...s[i],...n,timestamp:Date.now()}:(s.unshift({...n,timestamp:Date.now()}),s.length>100&&(s=s.slice(0,100))),{history:s,currentHistoryIndex:0}})},removeFromHistory:n=>{t(a=>({history:a.history.filter(i=>i.id!==n)}))},clearHistory:()=>{t({history:[],currentHistoryIndex:-1})},addBookmark:n=>{t(a=>a.bookmarks.some(s=>s.id===n.id)?a:{bookmarks:[{...n,timestamp:Date.now()},...a.bookmarks]})},removeBookmark:n=>{t(a=>({bookmarks:a.bookmarks.filter(i=>i.id!==n)}))},addToFavorites:n=>{t(a=>a.favorites.some(s=>s.id===n.id)?a:{favorites:[{...n,timestamp:Date.now()},...a.favorites]})},removeFromFavorites:n=>{t(a=>({favorites:a.favorites.filter(i=>i.id!==n)}))},startNewSession:n=>{t({currentSession:[n],currentSessionIndex:0})},addToCurrentSession:n=>{t(a=>a.currentSession.some(s=>s.id===n.id)?{currentSessionIndex:a.currentSession.findIndex(l=>l.id===n.id)}:{currentSession:[...a.currentSession,n],currentSessionIndex:a.currentSession.length})},navigateInSession:n=>{const a=o(),{currentSession:i,currentSessionIndex:s}=a;if(i.length===0)return null;let l=s;if(n==="prev"&&s>0)l=s-1;else if(n==="next"&&s<i.length-1)l=s+1;else return null;return t({currentSessionIndex:l}),i[l]},navigateToNext:async()=>Promise.resolve(),navigateToPrev:async()=>Promise.resolve(),updateReadingProgress:(n,a)=>{t(i=>{const s=l=>l.map(u=>u.id===n?{...u,readingProgress:Math.max(0,Math.min(100,a))}:u);return{history:s(i.history),bookmarks:s(i.bookmarks),favorites:s(i.favorites),currentSession:s(i.currentSession)}})},searchChapters:n=>{const a=o();return[...a.history,...a.bookmarks,...a.favorites].filter((l,u,v)=>v.findIndex(g=>g.id===l.id)===u).filter(l=>l.title.toLowerCase().includes(n.toLowerCase())||l.content.toLowerCase().includes(n.toLowerCase()))},getChaptersBySite:n=>o().history.filter(i=>i.site===n),getRecentChapters:(n=10)=>o().history.sort((i,s)=>s.timestamp-i.timestamp).slice(0,n)}),{name:"novel-reader-chapters",version:1})),Le="nr__readerContainer__Al1-y",Ee="nr__progressBar__vub5d",Re="nr__progressFill__av5Op",Te="nr__readerHeader__9ynG9",Ie="nr__headerLeft__v3xCr",Me="nr__headerRight__LPq38",He="nr__chapterTitle__DD2NQ",Ae="nr__exitButton__ZODXY",De="nr__headerButton__a9D8y",Pe="nr__active__amugk",Fe="nr__readerMain__GGPRo",Oe="nr__readerContent__25cl1",Ve="nr__chapterContent__C2poS",$e="nr__paywallNotice__2ihjV",ze="nr__paywallIcon__dGA--",Ue="nr__paywallButton__rW5Jx",qe="nr__additionalContent__YJutg",Ge="nr__authorInfo__leguK",We="nr__commentsSection__UH1Zf",Ze="nr__chapterNavigation__Bb-2R",Je="nr__navButton__RDqKX",Ke="nr__primary__QYJq1",Ye="nr__navControls__yVxW-",Xe="nr__controlButton__7ZH5N",Qe="nr__floatingControls__hFOqS",et="nr__floatingButton__VVnmA",tt="nr__sidePanel__jF4jf",nt="nr__open__Sbyzp",ot="nr__panelHeader__HMhLr",st="nr__panelTitle__qjSdR",rt="nr__panelCloseButton__DH0wO",at="nr__panelContent__Kraiw",it="nr__settingGroup__QkUdj",lt="nr__settingLabel__UrSxl",ct="nr__settingSlider__Vejt2",dt="nr__settingSelect__IuPbu",ut="nr__themeButtons__PYpQP",ht="nr__themeButton__GDZtD",mt="nr__bookmarkList__a93Pf",pt="nr__bookmarkItem__wZ5-s",ft="nr__bookmarkTitle__i2rv-",gt="nr__bookmarkTime__oFY1A",vt="nr__notification__oAUNQ",xt="nr__lightTheme__L5cIw",yt="nr__darkTheme__Bmiml",_t="nr__sepiaTheme__Ber-L",St="nr__fadeIn__b-29o",r={readerContainer:Le,progressBar:Ee,progressFill:Re,readerHeader:Te,headerLeft:Ie,headerRight:Me,chapterTitle:He,exitButton:Ae,headerButton:De,active:Pe,readerMain:Fe,readerContent:Oe,chapterContent:Ve,paywallNotice:$e,paywallIcon:ze,paywallButton:Ue,additionalContent:qe,authorInfo:Ge,commentsSection:We,chapterNavigation:Ze,navButton:Je,primary:Ke,navControls:Ye,controlButton:Xe,floatingControls:Qe,floatingButton:et,sidePanel:tt,open:nt,panelHeader:ot,panelTitle:st,panelCloseButton:rt,panelContent:at,settingGroup:it,settingLabel:lt,settingSlider:ct,settingSelect:dt,themeButtons:ut,themeButton:ht,bookmarkList:mt,bookmarkItem:pt,bookmarkTitle:ft,bookmarkTime:gt,notification:vt,lightTheme:xt,darkTheme:yt,sepiaTheme:_t,fadeIn:St},bt=()=>{const{settings:t,showSettings:o,updateSettings:n,toggleSettings:a}=w(),i=[{value:"Microsoft YaHei, -apple-system, BlinkMacSystemFont, sans-serif",label:"微软雅黑"},{value:"SimSun, serif",label:"宋体"},{value:"KaiTi, serif",label:"楷体"},{value:"SimHei, sans-serif",label:"黑体"},{value:"-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif",label:"系统字体"}],s=[{value:"light",label:"日间",icon:"☀️"},{value:"dark",label:"夜间",icon:"🌙"},{value:"sepia",label:"护眼",icon:"📖"}];return e.jsxs("div",{className:`${r.sidePanel} ${o?r.open:""}`,children:[e.jsxs("div",{className:r.panelHeader,children:[e.jsx("h3",{className:r.panelTitle,children:"阅读设置"}),e.jsx("button",{className:r.panelCloseButton,onClick:a,children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})})})]}),e.jsxs("div",{className:r.panelContent,children:[e.jsxs("div",{className:r.settingGroup,children:[e.jsx("label",{className:r.settingLabel,children:"阅读主题"}),e.jsx("div",{className:r.themeButtons,children:s.map(l=>e.jsxs("button",{className:`${r.themeButton} ${t.theme===l.value?r.active:""}`,onClick:()=>n({theme:l.value}),children:[e.jsx("span",{style:{marginRight:"4px"},children:l.icon}),l.label]},l.value))})]}),e.jsxs("div",{className:r.settingGroup,children:[e.jsxs("label",{className:r.settingLabel,children:["字体大小 (",t.fontSize,"px)"]}),e.jsx("input",{type:"range",min:"12",max:"28",step:"1",value:t.fontSize,onChange:l=>n({fontSize:parseInt(l.target.value)}),className:r.settingSlider}),e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",fontSize:"12px",color:"var(--label-color)",marginTop:"4px"},children:[e.jsx("span",{children:"12px"}),e.jsx("span",{children:"20px"}),e.jsx("span",{children:"28px"})]})]}),e.jsxs("div",{className:r.settingGroup,children:[e.jsx("label",{className:r.settingLabel,children:"字体类型"}),e.jsx("select",{value:t.fontFamily,onChange:l=>n({fontFamily:l.target.value}),className:r.settingSelect,children:i.map(l=>e.jsx("option",{value:l.value,children:l.label},l.value))})]}),e.jsxs("div",{className:r.settingGroup,children:[e.jsxs("label",{className:r.settingLabel,children:["行间距 (",t.lineHeight,")"]}),e.jsx("input",{type:"range",min:"1.2",max:"2.5",step:"0.1",value:t.lineHeight,onChange:l=>n({lineHeight:parseFloat(l.target.value)}),className:r.settingSlider}),e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",fontSize:"12px",color:"var(--label-color)",marginTop:"4px"},children:[e.jsx("span",{children:"紧密"}),e.jsx("span",{children:"适中"}),e.jsx("span",{children:"宽松"})]})]}),e.jsxs("div",{className:r.settingGroup,children:[e.jsxs("label",{className:r.settingLabel,children:["页面宽度 (",t.pageWidth,"px)"]}),e.jsx("input",{type:"range",min:"600",max:"1200",step:"50",value:t.pageWidth,onChange:l=>n({pageWidth:parseInt(l.target.value)}),className:r.settingSlider}),e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",fontSize:"12px",color:"var(--label-color)",marginTop:"4px"},children:[e.jsx("span",{children:"窄屏"}),e.jsx("span",{children:"适中"}),e.jsx("span",{children:"宽屏"})]})]}),e.jsxs("div",{className:r.settingGroup,children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"8px"},children:[e.jsx("label",{className:r.settingLabel,style:{margin:0},children:"自动滚动"}),e.jsxs("label",{className:r.toggleSwitch,children:[e.jsx("input",{type:"checkbox",checked:t.autoScroll,onChange:l=>n({autoScroll:l.target.checked})}),e.jsx("span",{className:r.toggleSlider})]})]}),t.autoScroll&&e.jsxs(e.Fragment,{children:[e.jsxs("label",{className:r.settingLabel,children:["滚动速度 (",t.scrollSpeed,")"]}),e.jsx("input",{type:"range",min:"10",max:"100",step:"5",value:t.scrollSpeed,onChange:l=>n({scrollSpeed:parseInt(l.target.value)}),className:r.settingSlider}),e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",fontSize:"12px",color:"var(--label-color)",marginTop:"4px"},children:[e.jsx("span",{children:"慢"}),e.jsx("span",{children:"适中"}),e.jsx("span",{children:"快"})]})]})]}),e.jsxs("div",{className:r.settingGroup,children:[e.jsx("label",{className:r.settingLabel,children:"快捷键"}),e.jsxs("div",{className:r.shortcutList,children:[e.jsxs("div",{className:r.shortcutItem,children:[e.jsx("span",{className:r.shortcutKey,children:"ESC"}),e.jsx("span",{className:r.shortcutDesc,children:"退出阅读模式"})]}),e.jsxs("div",{className:r.shortcutItem,children:[e.jsx("span",{className:r.shortcutKey,children:"← →"}),e.jsx("span",{className:r.shortcutDesc,children:"上一章/下一章"})]}),e.jsxs("div",{className:r.shortcutItem,children:[e.jsx("span",{className:r.shortcutKey,children:"空格"}),e.jsx("span",{className:r.shortcutDesc,children:"下一章"})]}),e.jsxs("div",{className:r.shortcutItem,children:[e.jsx("span",{className:r.shortcutKey,children:"Ctrl+B"}),e.jsx("span",{className:r.shortcutDesc,children:"添加书签"})]}),e.jsxs("div",{className:r.shortcutItem,children:[e.jsx("span",{className:r.shortcutKey,children:"Ctrl+S"}),e.jsx("span",{className:r.shortcutDesc,children:"打开设置"})]})]})]}),e.jsx("div",{className:r.settingGroup,children:e.jsx("button",{className:r.resetButton,onClick:()=>{n({fontSize:16,fontFamily:"Microsoft YaHei, -apple-system, BlinkMacSystemFont, sans-serif",lineHeight:1.8,theme:"light",pageWidth:800,autoScroll:!1,scrollSpeed:50})},children:"重置为默认设置"})})]})]})},Ct=()=>{const{bookmarks:t,favorites:o,history:n,removeBookmark:a,removeFromFavorites:i,addToFavorites:s,addBookmark:l}=A(),{loadChapter:u,toggleBookmarks:v}=w(),[g,h]=j.useState("bookmarks"),_=c=>{u(c),v()},x=c=>{o.some(d=>d.id===c.id)?i(c.id):s(c)},f=c=>new Date(c).toLocaleDateString("zh-CN",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),y=(c,m)=>c.length===0?e.jsxs("div",{className:r.emptyState,children:[e.jsx("svg",{width:"48",height:"48",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V5H19V19Z"})}),e.jsxs("p",{children:["暂无",m==="bookmarks"?"书签":m==="favorites"?"收藏":"历史记录"]})]}):e.jsx("div",{className:r.chapterList,children:c.map(d=>e.jsxs("div",{className:r.chapterItem,children:[e.jsxs("div",{className:r.chapterInfo,onClick:()=>_(d),children:[e.jsx("h4",{className:r.chapterItemTitle,children:d.title}),e.jsxs("div",{className:r.chapterMeta,children:[e.jsx("span",{className:r.siteName,children:d.site}),e.jsx("span",{className:r.timestamp,children:f(d.timestamp)})]}),d.readingProgress>0&&e.jsxs("div",{className:r.readingProgress,children:[e.jsx("div",{className:r.progressBar,style:{width:`${d.readingProgress}%`}}),e.jsxs("span",{className:r.progressText,children:[Math.round(d.readingProgress),"%"]})]})]}),e.jsxs("div",{className:r.chapterActions,children:[m!=="favorites"&&e.jsx("button",{className:`${r.actionButton} ${o.some(p=>p.id===d.id)?r.favorited:""}`,onClick:()=>x(d),title:o.some(p=>p.id===d.id)?"取消收藏":"添加收藏",children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M12,21.35L10.55,20.03C5.4,15.36 2,12.27 2,8.5 2,5.41 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.08C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.41 22,8.5C22,12.27 18.6,15.36 13.45,20.03L12,21.35Z"})})}),m==="bookmarks"&&e.jsx("button",{className:r.actionButton,onClick:()=>a(d.id),title:"删除书签",children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M19,6.41L17.59,5 12,10.59 6.41,5 5,6.41 10.59,12 5,17.59 6.41,19 12,13.41 17.59,19 19,17.59 13.41,12z"})})}),m==="favorites"&&e.jsx("button",{className:r.actionButton,onClick:()=>i(d.id),title:"取消收藏",children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M19,6.41L17.59,5 12,10.59 6.41,5 5,6.41 10.59,12 5,17.59 6.41,19 12,13.41 17.59,19 19,17.59 13.41,12z"})})})]})]},d.id))});return e.jsxs("div",{className:r.sidePanel,children:[e.jsxs("div",{className:r.panelHeader,children:[e.jsx("h3",{children:"阅读记录"}),e.jsx("button",{className:r.closeButton,onClick:v,children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M19,6.41L17.59,5 12,10.59 6.41,5 5,6.41 10.59,12 5,17.59 6.41,19 12,13.41 17.59,19 19,17.59 13.41,12z"})})})]}),e.jsxs("div",{className:r.panelTabs,children:[e.jsxs("button",{className:`${r.tabButton} ${g==="bookmarks"?r.active:""}`,onClick:()=>h("bookmarks"),children:["书签 (",t.length,")"]}),e.jsxs("button",{className:`${r.tabButton} ${g==="favorites"?r.active:""}`,onClick:()=>h("favorites"),children:["收藏 (",o.length,")"]}),e.jsxs("button",{className:`${r.tabButton} ${g==="history"?r.active:""}`,onClick:()=>h("history"),children:["历史 (",n.length,")"]})]}),e.jsxs("div",{className:r.panelContent,children:[g==="bookmarks"&&y(t,"bookmarks"),g==="favorites"&&y(o,"favorites"),g==="history"&&y(n.slice(0,50),"history")]})]})},jt=({loadingNext:t,loadingPrev:o,onNext:n,onPrev:a,onAddBookmark:i})=>{const{currentChapter:s,toggleBookmarks:l}=w();return e.jsxs("nav",{className:r.chapterNavigation,children:[e.jsxs("button",{className:r.navButton,onClick:a,disabled:o||!(s!=null&&s.prevChapterUrl),title:"上一章 (←)",children:[o?e.jsx("div",{className:r.loadingSpinner}):e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z"})}),"上一章"]}),e.jsxs("div",{className:r.navControls,children:[e.jsx("button",{className:r.controlButton,onClick:i,title:"添加书签 (Ctrl+B)",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M17,18L12,15.82L7,18V5H17M17,3H7A2,2 0 0,0 5,5V21L12,18L19,21V5C19,3.89 18.1,3 17,3Z"})})}),e.jsx("button",{className:r.controlButton,onClick:l,title:"书签目录",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M3,13H15V11H3M3,6V8H21V6M3,18H9V16H3V18Z"})})}),e.jsxs("div",{className:r.progressIndicator,children:[e.jsx("div",{className:r.progressDot}),e.jsxs("div",{className:r.progressText,children:[Math.round(w.getState().readingPosition*100),"%"]})]}),e.jsx("button",{className:r.controlButton,onClick:()=>window.scrollTo({top:0,behavior:"smooth"}),title:"回到顶部",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M7.41,15.41L12,10.83L16.59,15.41L18,14L12,8L6,14L7.41,15.41Z"})})}),e.jsx("button",{className:r.controlButton,onClick:()=>window.scrollTo({top:document.body.scrollHeight,behavior:"smooth"}),title:"回到底部",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z"})})})]}),e.jsxs("button",{className:`${r.navButton} ${r.primary}`,onClick:n,disabled:t||!(s!=null&&s.nextChapterUrl),title:"下一章 (→ 或 空格)",children:["下一章",t?e.jsx("div",{className:r.loadingSpinner}):e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"})})]})]})},wt="nr__floatingContainer__E1efY",kt="nr__animate__pchya",Nt="nr__floatingButton__1WRBT",Bt="nr__buttonContent__10ygT",Lt="nr__buttonText__OTGS0",Et="nr__pulseRing__BvWya",Rt="nr__pulse__glFNx",Tt="nr__tooltip__Q3hbD",It="nr__tooltipArrow__kNIL1",C={floatingContainer:wt,animate:kt,floatingButton:Nt,buttonContent:Bt,buttonText:Lt,pulseRing:Et,pulse:Rt,tooltip:Tt,tooltipArrow:It},Mt=({onNext:t,onPrev:o,onAddBookmark:n})=>{const[a,i]=j.useState(!1),s=()=>{i(!a)};return e.jsxs("div",{className:`${C.floatingControls} ${a?C.expanded:""}`,children:[e.jsx("button",{className:C.mainButton,onClick:s,title:a?"收起":"展开控制",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor",style:{transform:a?"rotate(45deg)":"rotate(0deg)"},children:e.jsx("path",{d:"M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"})})}),a&&e.jsxs("div",{className:C.controlButtons,children:[e.jsx("button",{className:C.controlButton,onClick:o,title:"上一章 (←)",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z"})})}),e.jsx("button",{className:C.controlButton,onClick:n,title:"添加书签 (Ctrl+B)",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M17,3H7A2,2 0 0,0 5,5V21L12,18L19,21V5C19,3.89 18.1,3 17,3Z"})})}),e.jsx("button",{className:C.controlButton,onClick:t,title:"下一章 (→)",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"})})})]})]})},Ht=()=>{const{settings:t,currentChapter:o,isReaderMode:n,showSettings:a,showBookmarks:i,readingPosition:s,exitReaderMode:l,updateReadingPosition:u,toggleSettings:v,toggleBookmarks:g}=w(),{navigateToNext:h,navigateToPrev:_,loadingNext:x,loadingPrev:f,addBookmark:y}=A(),[c,m]=j.useState(0),[d,p]=j.useState(!1),S=j.useCallback(b=>{if(n)switch(b.key){case"Escape":l();break;case"ArrowLeft":b.preventDefault(),_();break;case"ArrowRight":b.preventDefault(),h();break;case" ":b.preventDefault(),h();break;case"b":(b.ctrlKey||b.metaKey)&&(b.preventDefault(),B());break;case"s":(b.ctrlKey||b.metaKey)&&(b.preventDefault(),v());break}},[n,l,h,_,v]),N=j.useCallback(()=>{const b=window.pageYOffset||document.documentElement.scrollTop,L=document.documentElement.scrollHeight-window.innerHeight,D=L>0?b/L:0;m(D),u(D),p(b>200)},[u]),B=j.useCallback(()=>{o&&(y(o,s),At("书签已添加"))},[o,s,y]);if(j.useEffect(()=>{if(n){document.addEventListener("keydown",S),window.addEventListener("scroll",N,{passive:!0});const b=L=>{["ArrowLeft","ArrowRight"," "].includes(L.key)&&L.preventDefault()};return document.addEventListener("keydown",b),()=>{document.removeEventListener("keydown",S),window.removeEventListener("scroll",N),document.removeEventListener("keydown",b)}}},[n,S,N]),!n||!o)return null;const Z={light:r.lightTheme,dark:r.darkTheme,sepia:r.sepiaTheme}[t.theme];return e.jsxs("div",{className:`${r.readerContainer} ${Z}`,style:{"--reader-font-size":`${t.fontSize}px`,"--reader-font-family":t.fontFamily,"--reader-line-height":t.lineHeight,"--reader-page-width":`${t.pageWidth}px`},children:[e.jsx("div",{className:r.progressBar,children:e.jsx("div",{className:r.progressFill,style:{width:`${c*100}%`}})}),e.jsxs("header",{className:r.readerHeader,children:[e.jsx("div",{className:r.headerLeft,children:e.jsx("button",{className:r.exitButton,onClick:l,title:"退出阅读模式 (ESC)",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})})})}),e.jsx("h1",{className:r.chapterTitle,title:o.title,children:o.title}),e.jsxs("div",{className:r.headerRight,children:[e.jsx("button",{className:`${r.headerButton} ${i?r.active:""}`,onClick:g,title:"书签",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M17,3H7A2,2 0 0,0 5,5V21L12,18L19,21V5C19,3.89 18.1,3 17,3Z"})})}),e.jsx("button",{className:`${r.headerButton} ${a?r.active:""}`,onClick:v,title:"设置 (Ctrl+S)",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"})})})]})]}),e.jsx("main",{className:r.readerMain,children:e.jsx("div",{className:r.readerContent,children:o.isPaid&&!o.hasAccess?e.jsxs("div",{className:r.paywallNotice,children:[e.jsx("div",{className:r.paywallIcon,children:e.jsx("svg",{width:"48",height:"48",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z"})})}),e.jsx("h3",{children:"此章节需要付费阅读"}),e.jsx("p",{children:"请前往原网站完成付费后继续阅读"}),e.jsx("button",{className:r.paywallButton,onClick:()=>window.open(o.url,"_blank"),children:"前往付费"})]}):e.jsxs(e.Fragment,{children:[e.jsx("article",{className:r.chapterContent,dangerouslySetInnerHTML:{__html:o.content}}),e.jsxs("aside",{className:r.additionalContent,children:[o.authorInfo&&e.jsx("div",{className:r.authorInfo,dangerouslySetInnerHTML:{__html:o.authorInfo}}),o.comments&&e.jsx("div",{className:r.commentsSection,dangerouslySetInnerHTML:{__html:o.comments}})]})]})})}),a&&e.jsx(bt,{}),i&&e.jsx(Ct,{}),e.jsx(jt,{loadingNext:x,loadingPrev:f,onNext:h,onPrev:_,onAddBookmark:B}),d&&e.jsx(Mt,{onNext:h,onPrev:_,onAddBookmark:B})]})};function At(t){const o=document.createElement("div");o.className=r.notification,o.textContent=t,document.body.appendChild(o),setTimeout(()=>{o.style.opacity="0",setTimeout(()=>{document.body.removeChild(o)},300)},2e3)}const Dt=({onClick:t})=>{const{isReaderMode:o}=w(),[n,a]=j.useState(!1),[i,s]=j.useState(!1),l=!o;return j.useEffect(()=>{if(l){const u=setTimeout(()=>{a(!0),s(!0)},1e3);return()=>clearTimeout(u)}else a(!1),s(!1)},[l]),n?e.jsxs("div",{className:`${C.floatingContainer} ${i?C.animate:""}`,children:[e.jsxs("button",{className:C.floatingButton,onClick:t,title:"进入阅读模式",children:[e.jsxs("div",{className:C.buttonContent,children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V5H19V19M17,17H7V16H17V17M17,15H7V14H17V15M17,13H7V12H17V13M17,11H7V10H17V11M17,9H7V8H17V9Z"})}),e.jsx("span",{className:C.buttonText,children:"阅读模式"})]}),e.jsx("div",{className:C.pulseRing}),e.jsx("div",{className:C.pulseRing,style:{animationDelay:"1s"}})]}),e.jsxs("div",{className:C.tooltip,children:["点击进入沉浸式阅读模式",e.jsx("div",{className:C.tooltipArrow})]})]}):null},Pt={"jjwxc.net":{domain:"jjwxc.net",name:"晋江文学城",selectors:{title:".noveltext h2, .readtitle, h1",content:".noveltext, .readtext, .text",nextChapter:'a[title*="下一章"], .nextpage, .next',prevChapter:'a[title*="上一章"], .prevpage, .prev',comments:".comment-area, .pl, .review",authorInfo:".author-info, .readsmall, .author",paywall:".vip-content, .buyvip, .pay-chapter"},contentProcessor:t=>{const o=t.cloneNode(!0);o.querySelectorAll(".ad, .advertisement, .sponsor").forEach(a=>a.remove()),o.querySelectorAll("script, style").forEach(a=>a.remove());let n=o.innerHTML;return n=n.replace(/<br\s*\/?>/gi,`
`),n=n.replace(/\n\s*\n/g,`

`),n},urlProcessor:t=>t.startsWith("/")?`https://${window.location.hostname}${t}`:t},"qidian.com":{domain:"qidian.com",name:"起点中文网",selectors:{title:".chapter-title, .read-title, h3.title",content:".read-content, .chapter-content, .text-content",nextChapter:".chapter-control .next, .read-nav .next",prevChapter:".chapter-control .prev, .read-nav .prev",comments:".chapter-review, .comment-wrap",authorInfo:".author-intro, .book-info",paywall:".vip-chapter, .pay-chapter"},contentProcessor:t=>{const o=t.cloneNode(!0);o.querySelectorAll(".ad, .qd_om, .volume-wrap").forEach(a=>a.remove());let n=o.innerHTML;return n=n.replace(/<p[^>]*>/gi,"<p>"),n=n.replace(/&nbsp;/g," "),n}},"zongheng.com":{domain:"zongheng.com",name:"纵横中文网",selectors:{title:".title, .chapter_title, h1",content:".content, .chapter_content, .readcontent",nextChapter:".page_chapter .next, .readpage .next",prevChapter:".page_chapter .prev, .readpage .prev",comments:".book-comment, .chapter-comment",authorInfo:".author-info, .bookinfo",paywall:".vip-info, .pay-info"},contentProcessor:t=>{const o=t.cloneNode(!0);return o.querySelectorAll(".ad, .float-ad, .chapter-ad").forEach(n=>n.remove()),o.innerHTML}}};function Ft(){const t=window.location.hostname;for(const o of Object.values(Pt))if(t.includes(o.domain))return o;return null}async function Ot(t,o=document){var n,a,i;try{const s=o.querySelector(t.selectors.title),l=((n=s==null?void 0:s.textContent)==null?void 0:n.trim())||"未知章节",u=o.querySelector(t.selectors.content);if(!u)throw new Error("无法找到章节内容");let v=t.contentProcessor?t.contentProcessor(u):u.innerHTML;v=Vt(v);const h=!!(t.selectors.paywall?o.querySelector(t.selectors.paywall):null),_=!h||$t(o),x=o.querySelector(t.selectors.nextChapter),f=o.querySelector(t.selectors.prevChapter),y=x!=null&&x.href?t.urlProcessor?t.urlProcessor(x.href):x.href:void 0,c=f!=null&&f.href?t.urlProcessor?t.urlProcessor(f.href):f.href:void 0,m=t.selectors.authorInfo?(a=o.querySelector(t.selectors.authorInfo))==null?void 0:a.outerHTML:void 0,d=t.selectors.comments?(i=o.querySelector(t.selectors.comments))==null?void 0:i.outerHTML:void 0;return{id:zt(window.location.href),title:l,content:v,url:window.location.href,isPaid:h,hasAccess:_,nextChapterUrl:y,prevChapterUrl:c,authorInfo:m,comments:d}}catch(s){return console.error("提取章节内容失败:",s),null}}function Vt(t){return t=t.replace(/<script[^>]*>[\s\S]*?<\/script>/gi,""),t=t.replace(/<style[^>]*>[\s\S]*?<\/style>/gi,""),t=t.replace(/<!--[\s\S]*?-->/g,""),t=t.replace(/\s+/g," "),t=t.replace(/>\s+</g,"><"),t=t.replace(/<p[^>]*>\s*<\/p>/gi,""),t=t.replace(/<div[^>]*>\s*<\/div>/gi,""),t=t.replace(/<p[^>]*>/gi,"<p>"),t=t.replace(/\n\s*\n/g,`

`),t.trim()}function $t(t){var n;const o=[".vip-content",".pay-chapter",".buyvip",".need-pay"];for(const a of o){const i=t.querySelector(a);if(i&&(((n=i.textContent)==null?void 0:n.trim())||"").length<100)return!1}return!0}function zt(t){return btoa(encodeURIComponent(t)).replace(/[^a-zA-Z0-9]/g,"").slice(0,16)}class Ut{constructor(){this.root=null,this.container=null,this.floatingButton=null,this.siteConfig=null,this.init()}async init(){this.siteConfig=Ft(),this.siteConfig&&(document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>this.setup()):this.setup())}setup(){this.isNovelContentPage()&&(this.createFloatingButton(),this.createReactContainer(),this.observePageChanges())}isNovelContentPage(){if(!this.siteConfig)return!1;const o=document.querySelector(this.siteConfig.selectors.title),n=document.querySelector(this.siteConfig.selectors.content);return!!(o&&n)}createFloatingButton(){this.floatingButton&&this.floatingButton.remove();const o=document.createElement("div");o.id="novel-reader-floating-button",o.style.cssText=`
      position: fixed;
      bottom: 20px;
      right: 20px;
      z-index: 999998;
      pointer-events: none;
    `,document.body.appendChild(o),this.floatingButton=o,P(o).render(e.jsx(Dt,{onClick:this.enterReaderMode.bind(this)}))}createReactContainer(){this.container||(this.container=document.createElement("div"),this.container.id="novel-reader-extension-root",this.container.style.cssText=`
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 999999;
        pointer-events: none;
      `,document.body.appendChild(this.container),this.root=P(this.container),this.root.render(e.jsx(Ht,{})))}async enterReaderMode(){try{console.log("Starting to enter reader mode...");const o=await this.extractCurrentChapter();if(!o)throw console.error("无法提取章节内容"),this.showErrorMessage("无法提取章节内容，请确保在支持的小说页面上"),new Error("无法提取章节内容");console.log("Chapter info extracted:",o),w.getState().enterReaderMode(o),console.log("Reader mode state updated"),A.getState().addToHistory(o),console.log("Added to history"),this.floatingButton&&(this.floatingButton.style.display="none",console.log("Floating button hidden")),document.body.style.overflow="hidden",console.log("Page scroll disabled"),console.log("Reader mode entered successfully")}catch(o){throw console.error("进入阅读模式失败:",o),this.showErrorMessage("进入阅读模式失败，请稍后重试"),o}}async extractCurrentChapter(){return this.siteConfig?Ot(this.siteConfig,document):null}observePageChanges(){let o=window.location.href;new MutationObserver(()=>{window.location.href!==o&&(o=window.location.href,setTimeout(()=>{this.isNovelContentPage()?this.createFloatingButton():this.removeFloatingButton()},1e3))}).observe(document.body,{childList:!0,subtree:!0}),window.addEventListener("popstate",()=>{setTimeout(()=>{this.isNovelContentPage()?this.createFloatingButton():this.removeFloatingButton()},500)})}removeFloatingButton(){this.floatingButton&&(this.floatingButton.remove(),this.floatingButton=null)}showErrorMessage(o){const n=document.createElement("div");n.style.cssText=`
      position: fixed;
      top: 20px;
      right: 20px;
      background: #ff4757;
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      z-index: 1000000;
      box-shadow: 0 4px 20px rgba(255, 71, 87, 0.3);
      animation: slideInRight 0.3s ease;
    `,n.textContent=o,document.body.appendChild(n),setTimeout(()=>{n.style.animation="slideOutRight 0.3s ease",setTimeout(()=>{n.parentNode&&n.parentNode.removeChild(n)},300)},3e3)}}chrome.runtime.onMessage.addListener((t,o,n)=>{var a;switch(console.log("Content script received message:",t),t.type){case"TOGGLE_READER_MODE":const{isReaderMode:i}=w.getState();if(console.log("Current reader mode state:",i),i)console.log("Exiting reader mode..."),w.getState().exitReaderMode(),document.body.style.overflow="",n({success:!0,isReaderMode:!1});else{console.log("Entering reader mode...");const l=window.novelReaderExtension;l?l.enterReaderMode().then(()=>{const u=w.getState();console.log("Reader mode entered, new state:",u.isReaderMode),n({success:!0,isReaderMode:u.isReaderMode})}).catch(u=>{console.error("Failed to enter reader mode:",u),n({success:!1,error:u.message})}):(console.error("Extension instance not found"),n({success:!1,error:"Extension not initialized"}))}break;case"GET_READING_STATE":const s=w.getState();console.log("Getting reading state:",s),n({isReaderMode:s.isReaderMode,currentChapter:((a=s.currentChapter)==null?void 0:a.title)||null});break;default:console.log("Unknown message type:",t.type),n({success:!1,error:"Unknown message type"})}return!0});w.subscribe(t=>{if(console.log("Reader store state changed:",t),t.isReaderMode)console.log("Reader mode activated");else{console.log("Reader mode exited, restoring page state..."),document.body.style.overflow="";const o=window.novelReaderExtension;o&&o.floatingButton&&(o.floatingButton.style.display="block",console.log("Floating button restored"))}});const qt=new Ut;window.novelReaderExtension=qt;const W=document.createElement("style");W.textContent=`
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes slideOutRight {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
`;document.head.appendChild(W);
