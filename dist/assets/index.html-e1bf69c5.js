import"./modulepreload-polyfill-3cfb730f.js";import{c as _,j as e,r as c}from"./client-25e9187c.js";const p="nr__popup__FFXrc",x="nr__header__Hh2qu",C="nr__logo__e8j2K",L="nr__title__9KqAe",j="nr__content__c3aq-",f="nr__loading__YgRVo",m="nr__spinner__MI6OR",v="nr__spin__Y-7cu",g="nr__readerActive__yRRdI",M="nr__statusIcon__GF-ae",A="nr__chapterInfo__5OiYk",H="nr__readerInactive__OMkmG",V="nr__features__H5Cfc",N="nr__feature__uQWD6",w="nr__button__x7hP3",R="nr__primary__kxUsj",I="nr__secondary__djxwA",y="nr__footer__6mHYo",B="nr__footerButton__vsVR4",b="nr__pulse__0GGWy",r={popup:p,header:x,logo:C,title:L,content:j,loading:f,spinner:m,spin:v,readerActive:g,statusIcon:M,chapterInfo:A,readerInactive:H,features:V,feature:N,button:w,primary:R,secondary:I,footer:y,footerButton:B,pulse:b},E=()=>{const[i,a]=c.useState({isReaderMode:!1,currentChapter:null}),[h,s]=c.useState(!0);c.useEffect(()=>{chrome.tabs.query({active:!0,currentWindow:!0},n=>{var o;(o=n[0])!=null&&o.id?chrome.tabs.sendMessage(n[0].id,{type:"GET_READING_STATE"},t=>{t&&a(t),s(!1)}):s(!1)})},[]);const l=()=>{s(!0),console.log("Popup: Toggling reader mode..."),chrome.tabs.query({active:!0,currentWindow:!0},n=>{var o;(o=n[0])!=null&&o.id?(console.log("Popup: Sending message to tab:",n[0].id),chrome.tabs.sendMessage(n[0].id,{type:"TOGGLE_READER_MODE"},t=>{if(console.log("Popup: Received response:",t),chrome.runtime.lastError){console.error("Popup: Chrome runtime error:",chrome.runtime.lastError),s(!1);return}t&&t.success?(a({isReaderMode:t.isReaderMode,currentChapter:t.currentChapter||null}),console.log("Popup: State updated:",t.isReaderMode)):console.error("Popup: Failed to toggle reader mode:",t==null?void 0:t.error),s(!1)})):(console.error("Popup: No active tab found"),s(!1))})},u=()=>{chrome.runtime.openOptionsPage()};return h?e.jsx("div",{className:r.popup,children:e.jsxs("div",{className:r.loading,children:[e.jsx("div",{className:r.spinner}),e.jsx("p",{children:"检测页面状态中..."})]})}):e.jsxs("div",{className:r.popup,children:[e.jsxs("div",{className:r.header,children:[e.jsx("div",{className:r.logo,children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V5H19V19M17,17H7V16H17V17M17,15H7V14H17V15M17,13H7V12H17V13M17,11H7V10H17V11M17,9H7V8H17V9Z"})})}),e.jsxs("div",{className:r.title,children:[e.jsx("h2",{children:"小说阅读助手"}),e.jsx("p",{children:"优化您的阅读体验"})]})]}),e.jsx("div",{className:r.content,children:i.isReaderMode?e.jsxs("div",{className:r.readerActive,children:[e.jsx("div",{className:r.statusIcon,children:e.jsx("svg",{width:"32",height:"32",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M11,16.5L18,9.5L16.59,8.09L11,13.67L7.91,10.59L6.5,12L11,16.5Z"})})}),e.jsx("h3",{children:"阅读模式已激活"}),i.currentChapter&&e.jsxs("p",{className:r.chapterInfo,children:["正在阅读: ",i.currentChapter]}),e.jsx("button",{className:`${r.button} ${r.secondary}`,onClick:l,children:"退出阅读模式"})]}):e.jsxs("div",{className:r.readerInactive,children:[e.jsx("div",{className:r.statusIcon,children:e.jsx("svg",{width:"32",height:"32",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"})})}),e.jsx("h3",{children:"在小说页面启用阅读模式"}),e.jsx("p",{children:"支持晋江、起点、纵横等主流网站"}),e.jsxs("div",{className:r.features,children:[e.jsxs("div",{className:r.feature,children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M12,17.27L18.18,21L16.54,13.97L22,9.24L14.81,8.62L12,2L9.19,8.62L2,9.24L7.46,13.97L5.82,21L12,17.27Z"})}),e.jsx("span",{children:"沉浸式阅读体验"})]}),e.jsxs("div",{className:r.feature,children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"})}),e.jsx("span",{children:"智能章节切换"})]}),e.jsxs("div",{className:r.feature,children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"})}),e.jsx("span",{children:"个性化设置"})]}),e.jsxs("div",{className:r.feature,children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M17,18L12,15.82L7,18V5H17M17,3H7A2,2 0 0,0 5,5V21L12,18L19,21V5C19,3.89 18.1,3 17,3Z"})}),e.jsx("span",{children:"书签和历史记录"})]})]}),e.jsx("button",{className:`${r.button} ${r.primary}`,onClick:l,children:"启用阅读模式"})]})}),e.jsxs("div",{className:r.footer,children:[e.jsxs("button",{className:r.footerButton,onClick:u,children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"})}),"设置"]}),e.jsxs("button",{className:r.footerButton,onClick:()=>window.open("https://github.com/novel-reader-extension","_blank"),children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M12,2.04C6.5,2.04 2,6.53 2,12.04C2,17.54 6.5,22.04 12,22.04C17.5,22.04 22,17.54 22,12.04C22,6.53 17.5,2.04 12,2.04M12,20.04A8,8 0 0,1 4,12.04A8,8 0 0,1 12,4.04A8,8 0 0,1 20,12.04A8,8 0 0,1 12,20.04M13,14.5V16.5H11V14.5H13M13,8.5V12.5H11V8.5H13Z"})}),"帮助"]})]})]})},d=document.getElementById("root");d&&_(d).render(e.jsx(E,{}));
