#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('Running post-build fixes...');

// 复制并修复 manifest.json
const manifestSrcPath = path.join(__dirname, '../manifest.json');
const manifestDistPath = path.join(__dirname, '../dist/manifest.json');

try {
  // 复制并修复 manifest.json 到 dist 目录
  const manifestContent = fs.readFileSync(manifestSrcPath, 'utf8');
  const manifest = JSON.parse(manifestContent);

  // 修复 service worker 路径
  if (manifest.background && manifest.background.service_worker) {
    manifest.background.service_worker = 'service-worker-loader.js';
  }

  fs.writeFileSync(manifestDistPath, JSON.stringify(manifest, null, 2));
  console.log('✓ Copied and fixed manifest.json to dist');
} catch (error) {
  console.error('✗ Failed to copy manifest.json:', error.message);
}

// 创建 service-worker-loader.js
const serviceWorkerPath = path.join(__dirname, '../dist/service-worker-loader.js');
const backgroundScriptPath = path.join(__dirname, '../dist/assets/index.ts-d11b40de.js');

try {
  // 检查背景脚本是否存在
  if (fs.existsSync(backgroundScriptPath)) {
    // 读取背景脚本内容
    const backgroundScript = fs.readFileSync(backgroundScriptPath, 'utf8');

    // 创建新的 service worker 内容
    const serviceWorkerContent = `// Service Worker for Novel Reader Extension
${backgroundScript}`;

    // 写入 service worker 文件
    fs.writeFileSync(serviceWorkerPath, serviceWorkerContent);
    console.log('✓ Created service-worker-loader.js');
  } else {
    console.log('⚠ Background script not found, creating simple service worker');
    // 创建简单的 service worker
    const simpleServiceWorker = `// Service Worker for Novel Reader Extension
console.log('Novel Reader Extension service worker started');

// 基本的扩展功能
chrome.runtime.onInstalled.addListener(() => {
  console.log('Novel Reader Extension installed');
});`;
    fs.writeFileSync(serviceWorkerPath, simpleServiceWorker);
    console.log('✓ Created simple service-worker-loader.js');
  }

} catch (error) {
  console.error('✗ Failed to create service-worker-loader.js:', error.message);
}

console.log('Post-build fixes completed!');
